from managers.team_manager import get_team_manager


def case_reviewer_team(agent_manager, termination_manager):
    team_manager = get_team_manager()
    termination_condition = termination_manager.create_termination("max_message", max_messages=3)
    team = team_manager.create_autogen_team(
        team_class='RoundRobinGroupChat',
        participants=[agent_manager.get_agent_info('case_retrieval').get('function'), agent_manager.get_agent_info('case_reviewer').get('function')],
        termination_condition=termination_condition
    )
    return team